import { Stack } from "expo-router";

export default function AuthLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: true,
        headerShadowVisible: false,
        headerBackButtonDisplayMode: "minimal",
        headerBackVisible: true,
        headerTitle: "",
        headerStyle: {
          backgroundColor: '#ffffff',
        },
        headerTintColor: '#2078FF',
      }}
    >
      <Stack.Screen
        name="login/index"
        options={{
          headerShown: false
        }}
      />
      <Stack.Screen
        name="verify2FA/index"
        options={{
          headerShown: true,
          headerTitle: "Verification",
          headerBackVisible: true,
        }}
      />
    </Stack>
  );
}
