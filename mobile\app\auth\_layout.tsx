import { Stack } from 'expo-router';

export default function AuthLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: true,
        headerShadowVisible: false,
        headerBackButtonDisplayMode: "minimal",
        headerBackVisible: true,
        headerTitle: "",
        headerStyle: {
          backgroundColor: '#ffffff',
        },
      }}
    >
      <Stack.Screen
        name="login/index"
        options={{
          headerShown: true,
          headerTitle: "",
          headerBackVisible: true,
        }}
      />
      <Stack.Screen
        name="verify2FA/index"
        options={{
          headerShown: true,
          headerTitle: "",
          headerBackVisible: true,
        }}
      />
    </Stack>
  );
}
