import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert
} from 'react-native';
import tw from 'twrnc';
import { useAuth } from '../../hooks/useAuth';
import { router } from 'expo-router';

export default function MainScreen() {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await logout();
            router.replace('/intro');
          },
        },
      ]
    );
  };

  const dashboardItems = [
    {
      title: 'Water Supply',
      description: 'Monitor water supply systems',
      icon: '💧',
      color: 'bg-blue-500',
    },
    {
      title: 'Sanitation',
      description: 'Track sanitation facilities',
      icon: '🚿',
      color: 'bg-green-500',
    },
    {
      title: 'Hygiene',
      description: 'Hygiene program management',
      icon: '🧼',
      color: 'bg-purple-500',
    },
    {
      title: 'Reports',
      description: 'Generate and view reports',
      icon: '📊',
      color: 'bg-orange-500',
    },
    {
      title: 'Data Collection',
      description: 'Collect field data',
      icon: '📝',
      color: 'bg-indigo-500',
    },
    {
      title: 'Settings',
      description: 'App and account settings',
      icon: '⚙️',
      color: 'bg-gray-500',
    },
  ];

  return (
    <View style={tw`flex-1 bg-gray-50`}>
      {/* Header */}
      <View style={tw`bg-blue-600 pt-12 pb-6 px-6`}>
        <View style={tw`flex-row justify-between items-center`}>
          <View>
            <Text style={tw`text-white text-2xl font-bold`}>
              WASH MIS
            </Text>
            <Text style={tw`text-blue-100 text-sm mt-1`}>
              Welcome back, {user?.firstName || user?.email}
            </Text>
          </View>
          <TouchableOpacity
            style={tw`bg-blue-700 px-4 py-2 rounded-lg`}
            onPress={handleLogout}
          >
            <Text style={tw`text-white text-sm font-medium`}>
              Logout
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Dashboard Content */}
      <ScrollView style={tw`flex-1`} contentContainerStyle={tw`p-6`}>
        {/* Quick Stats */}
        <View style={tw`bg-white rounded-lg p-6 mb-6 shadow-sm`}>
          <Text style={tw`text-lg font-bold text-gray-800 mb-4`}>
            Quick Overview
          </Text>
          <View style={tw`flex-row justify-between`}>
            <View style={tw`items-center`}>
              <Text style={tw`text-2xl font-bold text-blue-600`}>
                1,234
              </Text>
              <Text style={tw`text-gray-600 text-sm`}>
                Water Points
              </Text>
            </View>
            <View style={tw`items-center`}>
              <Text style={tw`text-2xl font-bold text-green-600`}>
                856
              </Text>
              <Text style={tw`text-gray-600 text-sm`}>
                Facilities
              </Text>
            </View>
            <View style={tw`items-center`}>
              <Text style={tw`text-2xl font-bold text-purple-600`}>
                92%
              </Text>
              <Text style={tw`text-gray-600 text-sm`}>
                Coverage
              </Text>
            </View>
          </View>
        </View>

        {/* Dashboard Grid */}
        <Text style={tw`text-lg font-bold text-gray-800 mb-4`}>
          Main Functions
        </Text>
        <View style={tw`flex-row flex-wrap justify-between`}>
          {dashboardItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={tw`w-[48%] bg-white rounded-lg p-4 mb-4 shadow-sm`}
              activeOpacity={0.7}
            >
              <View style={tw`${item.color} w-12 h-12 rounded-lg justify-center items-center mb-3`}>
                <Text style={tw`text-2xl`}>{item.icon}</Text>
              </View>
              <Text style={tw`text-gray-800 font-semibold text-base mb-1`}>
                {item.title}
              </Text>
              <Text style={tw`text-gray-600 text-sm leading-5`}>
                {item.description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Recent Activity */}
        <View style={tw`bg-white rounded-lg p-6 mt-4 shadow-sm`}>
          <Text style={tw`text-lg font-bold text-gray-800 mb-4`}>
            Recent Activity
          </Text>
          <View style={tw`space-y-3`}>
            <View style={tw`flex-row items-center py-2`}>
              <View style={tw`w-2 h-2 bg-green-500 rounded-full mr-3`} />
              <Text style={tw`text-gray-700 flex-1`}>
                Data collection completed in Nyamasheke
              </Text>
              <Text style={tw`text-gray-500 text-xs`}>
                2h ago
              </Text>
            </View>
            <View style={tw`flex-row items-center py-2`}>
              <View style={tw`w-2 h-2 bg-blue-500 rounded-full mr-3`} />
              <Text style={tw`text-gray-700 flex-1`}>
                New water point registered
              </Text>
              <Text style={tw`text-gray-500 text-xs`}>
                4h ago
              </Text>
            </View>
            <View style={tw`flex-row items-center py-2`}>
              <View style={tw`w-2 h-2 bg-orange-500 rounded-full mr-3`} />
              <Text style={tw`text-gray-700 flex-1`}>
                Monthly report generated
              </Text>
              <Text style={tw`text-gray-500 text-xs`}>
                1d ago
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}
