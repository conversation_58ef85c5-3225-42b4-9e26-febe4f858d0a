import Constants from 'expo-constants';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { AuthProvider } from '../contexts/AuthContext';
import { Inter_400Regular, Inter_500Medium, Inter_600SemiBold, Inter_700Bold, useFonts } from '@expo-google-fonts/inter';
import { useEffect } from 'react';
import * as SplashScreen from 'expo-splash-screen';
import { PaperProvider } from 'react-native-paper';

const PRIMARY_COLOR = '#2078FF';
const SECONDARY_COLOR = '#555555';


export default function RootLayout() {

  const [loaded, error] = useFonts({
    Inter_400Regular,
    Inter_500Medium,
    Inter_700Bold,
    Inter_600SemiBold
  });

  useEffect(() => {
    if (loaded || error) {
      SplashScreen.hideAsync();
    }
  }, [loaded, error]);

  if (!loaded && !error) {
    return null;
  }
  return (
    <PaperProvider
      theme={{
        colors: {
          primary: PRIMARY_COLOR,
          secondary: SECONDARY_COLOR,
          accent: '#555555',
          background: '#ffffff',
          surface: '#ffffff',
          error: '#f13a59',
          onPrimary: '#ffffff',
          onSecondary: '#ffffff',
          onError: '#ffffff',
        },
      }}
    >
      <AuthProvider>
        <SafeAreaView style={{ flex: 1 }}>
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: Constants.statusBarHeight,
            backgroundColor: PRIMARY_COLOR,
            zIndex: 1
          }} />
          <StatusBar style="light" />
          <Stack screenOptions={{ headerShown: false }} />
        </SafeAreaView>
      </AuthProvider>
    </PaperProvider>
  );
}
