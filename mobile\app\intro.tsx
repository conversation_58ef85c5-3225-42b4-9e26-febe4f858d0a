import AppButton from '@/components/ui/Button';
import AppText from '@/components/ui/Text';
import { router } from 'expo-router';
import React from 'react';
import { Image, View } from 'react-native';
import tw from 'twrnc';

export default function IntroScreen() {
  const handleGetStarted = () => {
    router.push('/auth/login');
  };

  return (
    <View style={tw`flex-1 bg-white`}>
      <View style={tw`flex-1 justify-between items-center px-5 pt-48 pb-4`}>
        <View style={tw`flex items-center gap-4`}>
            <View style={tw``}>
              <Image
                source={require('../assets/images/logo.png')} 
                style={tw`w-[200px] h-[200px]`}
              />
            </View>

            <AppText weight='bold' style={tw`text-3xl text-gray-800 text-center text-[#2078FF]`}>
              WASH MIS
            </AppText>
        </View>

        <AppText style={tw`text-gray-600 text-center text-lg -mt-8`}>
          WASH MIS is a comprehensive system that monitors and manages water supply, 
          sanitation, and hygiene services and makes interventions all over the country.
        </AppText>

        <AppButton title="Get Started" onPress={handleGetStarted} style={tw`w-full`} />
      </View>

    </View>
  );
}
