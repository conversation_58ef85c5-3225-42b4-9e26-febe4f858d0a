import { Text, TextProps } from 'react-native';

type FontWeight = 'regular' | 'semibold' | 'bold' | 'medium';

const fontMap: Record<FontWeight, string> = {
  regular: 'Inter_400Regular',
  medium: 'Inter_500Medium',
  semibold: 'Inter_600SemiBold',
  bold: 'Inter_700Bold',
};

export default function AppText({ style, ...props }: TextProps & { weight?: FontWeight }) {
  const fontWeight = props.weight ?? 'regular';
  return (
    <Text
      {...props}
      style={[{ fontFamily: fontMap[fontWeight] }, style]}
    />
  );
}
