import React from 'react';
import { View, TextInput, TextInputProps } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import tw from 'twrnc';

type AppInputProps = TextInputProps & {
  iconName: keyof typeof Ionicons.glyphMap;
};

export default function AppInput({ iconName, placeholder, ...props }: AppInputProps) {
  return (
    <View style={tw`flex-row items-center border border-gray-200 rounded px-3 py-2 bg-white`}>
      <Ionicons name={iconName} size={24} color="#6B7280" style={tw`ml-2`} />
      <TextInput
        style={[tw`flex-1 h-10 text-base text-gray-800`, {fontFamily: 'Inter_400Regular'}, props.style]}
        placeholder={placeholder}
        placeholderTextColor="#9CA3AF" 
        {...props}
      />
    </View>
  );
}
