import {
  TouchableOpacity,
  TouchableOpacityProps,
  ViewStyle,
  StyleProp,
  ActivityIndicator,
} from 'react-native';
import AppText from './Text';
import tw from 'twrnc';

type AppButtonProps = TouchableOpacityProps & {
  title: string;
  loading?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<import('react-native').TextStyle>;
};

export default function AppButton({
  title,
  onPress,
  style,
  textStyle,
  loading = false,
  disabled,
  ...rest
}: AppButtonProps) {
  return (
    <TouchableOpacity
      style={[
        tw`bg-blue-600 px-12 py-4 rounded-xl w-full items-center justify-center`,
        style,
        (loading || disabled) && tw`opacity-50`,
      ]}
      onPress={onPress}
      activeOpacity={0.8}
      disabled={loading || disabled}
      {...rest}
    >
      {loading ? (
        <ActivityIndicator color="#ffffff" />
      ) : (
        <AppText weight="semibold" style={[tw`text-white text-lg text-center`, textStyle]}>
          {title}
        </AppText>
      )}
    </TouchableOpacity>
  );
}