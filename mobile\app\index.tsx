import { router } from 'expo-router';
import React, { useEffect } from 'react';
import { ActivityIndicator, View } from 'react-native';
import tw from 'twrnc';
import { useAuth } from '../hooks/useAuth';

export default function Index() {
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated) {
        // User is authenticated, go to main app
        router.replace('/main');
      } else {
        // User is not authenticated, show intro
        router.replace('/intro');
      }
    }
  }, [isAuthenticated, isLoading]);

  // Show loading screen while determining auth state
  return (
    <View style={tw`flex-1 justify-center items-center bg-white`}>
      <ActivityIndicator size="large" color="#155E95" />
    </View>
  );
}
